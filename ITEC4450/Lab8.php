<!DOCTYPE html>
<html>
    <head>
        <title>Lab 8 - Sorting Data in Different Ways</title>
    </head>

    <body>
        <form method="POST" action="">
            <table style="background-color:pink;margin:auto;" border=0>
                <thead align="center">
                <tr>
                    <td colspan="2">Sorting information in different ways <br>
                        (Click <a href="lab8data.txt">here</a> to see the data)
                    </td>
                </tr>
                </thead>

                <tr>
                    <td> <hr/> </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="byname" checked>Sort by name in ascending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="byemail">Sort by email in descending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bymajor">Sort by major in ascending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bygrade">Sort by grade in descending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bymajorgrade">Sort by major in descending then by grade in ascending</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bygrademajorname">Sort by grade in descending then by major in ascending then by name in ascending</label>
                    </td>
                </tr>

                <tr>
                    <td align="center">
                        <input name="show" alt="Login" type="submit" value="submit">
                    </td>
                </tr>
            </table>
        </form>
        <br/>

        <?php
            if (isset($_POST['show'])) {
                $file = "lab8data.txt";
                $studentStr = file_get_contents($file);
                $studentStr = trim($studentStr);

                $studentList = explode("\n", $studentStr);
                foreach ($studentList as $index=>$student) {
                    $Students[$index] = explode("\t", $student);
                }

                function display2D($A)
                {
                    echo "<table border='1'>";
                    echo "<tr> <th>Name</th> <th>Email</th> <th>Major</th> <th>Grade</th> <th>IP Address</th> </tr>";
                    foreach ($A as $s)
                    {
                        echo "<tr>";
                            foreach ($s as $info)
                                echo "<td>".$info."</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }

                function cmpNameASC($a, $b)
                {
                    if ($a[0] == $b[0]) return 0;
                    elseif ($a[0] > $b[0]) return 1;
                    else return -1;
                }

                function cmpEmailDESC($a, $b)
                {
                    if ($a[1] == $b[1]) return 0;
                    elseif ($a[1] > $b[1]) return -1;
                    else return 1;
                }

                function cmpMajorASC($a, $b)
                {
                    if ($a[2] == $b[2]) return 0;
                    elseif ($a[2] > $b[2]) return 1;
                    else return -1;
                }

                function cmpGradeDESC($a, $b)
                {
                    if ($a[3] == $b[3]) return 0;
                    elseif ($a[3] > $b[3]) return -1;
                    else return 1;
                }

                function cmpMajorGrade($a, $b)
                {
                    $index1 = 2; // major
                    $index2 = 3; // grade

                    if ($a[$index1] == $b[$index1])
                    {
                        if ($a[$index2] == $b[$index2])
                            return 0;
                        elseif ($a[$index2] > $b[$index2])
                            return 1;
                        else
                            return -1;
                    }
                    elseif ($a[$index1] > $b[$index1]) return -1;
                    else return 1;
                }

                function cmpGradeMajorName($a, $b)
                {
                    $index1 = 3; // grade
                    $index2 = 2; // major
                    $index3 = 0; // name

                    if ($a[$index1] == $b[$index1])
                    {
                        if ($a[$index2] == $b[$index2])
                        {
                            if ($a[$index3] == $b[$index3]) return 0;
                            elseif ($a[$index3] > $b[$index3]) return 1;
                            else return -1;
                        }
                        elseif ($a[$index2] > $b[$index2])
                            return 1;
                        else
                            return -1;
                    }
                    elseif ($a[$index1] > $b[$index1]) return -1;
                    else return 1;
                }

                if ($_POST['sortby'] == 'byname')
                {
                    echo "Sorted by name in ascending order: <br>";
                    usort($Students, "cmpNameASC");
                    display2D($Students);
                }

                if ($_POST['sortby'] == 'byemail')
                {
                    echo "Sorted by email in descending order: <br>";
                    usort($Students, "cmpEmailDESC");
                    display2D($Students);
                }

                if ($_POST['sortby'] == 'bymajor')
                {
                    echo "Sorted by major in ascending order: <br>";
                    usort($Students, "cmpMajorASC");
                    display2D($Students);
                }

                if ($_POST['sortby'] == 'bygrade')
                {
                    echo "Sorted by grade in descending order: <br>";
                    usort($Students, "cmpGradeDESC");
                    display2D($Students);
                }

                if ($_POST['sortby'] == 'bymajorgrade')
                {
                    echo "Sorted by major in descending then by grade in ascending: <br>";
                    usort($Students, "cmpMajorGrade");
                    display2D($Students);
                }

                if ($_POST['sortby'] == 'bygrademajorname')
                {
                    echo "Sorted by grade in descending then by major in ascending then by name in ascending: <br>";
                    usort($Students, "cmpGradeMajorName");
                    display2D($Students);
                }
            }
        ?>
    </body>
</html>