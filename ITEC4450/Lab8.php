<!DOCTYPE html>
<html>
    <head>
        <title>Lab 8 - Sorting Data in Different Ways</title>
    </head>

    <body>
        <form method="POST" action="">
            <table style="background-color:pink;margin:auto;" border=0>
                <thead align="center">
                <tr>
                    <td colspan="2">Sorting information in different ways <br>
                        (Click <a href="lab8data.txt">here</a> to see the data)
                    </td>
                </tr>
                </thead>

                <tr>
                    <td> <hr/> </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="byname" checked>Sort by name in ascending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="byemail">Sort by email in descending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bymajor">Sort by major in ascending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bygrade">Sort by grade in descending order</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bymajorgrade">Sort by major in descending then by grade in ascending</label>
                    </td>
                </tr>

                <tr>
                    <td align=left>
                        <label><input type=radio name="sortby" value="bygrademajorname">Sort by grade in descending then by major in ascending then by name in ascending</label>
                    </td>
                </tr>

                <tr>
                    <td align="center">
                        <input name="show" alt="Login" type="submit" value="submit">
                    </td>
                </tr>
            </table>
        </form>
        <br/>

        <?php
            if (isset($_POST['show'])) {
                // Read data from file
                $data = array();
                $file = fopen("lab8data.txt", "r");

                if ($file) {
                    while (($line = fgets($file)) !== false) {
                        $line = trim($line);
                        if (!empty($line)) {
                            $parts = explode("\t", $line);
                            if (count($parts) >= 4) {
                                $data[] = array(
                                    'name' => $parts[0],
                                    'email' => $parts[1],
                                    'major' => $parts[2],
                                    'grade' => (int)$parts[3],
                                    'ip' => isset($parts[4]) ? $parts[4] : ''
                                );
                            }
                        }
                    }
                    fclose($file);
                }

                // Function to display data in table format
                function displayData($data) {
                    echo "<table border='1' style='margin:auto; border-collapse:collapse;'>";
                    echo "<tr style='background-color:#f0f0f0;'>";
                    echo "<th>Name</th><th>Email</th><th>Major</th><th>Grade</th><th>IP Address</th>";
                    echo "</tr>";

                    foreach ($data as $row) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['major']) . "</td>";
                        echo "<td>" . $row['grade'] . "</td>";
                        echo "<td>" . htmlspecialchars($row['ip']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }

                // Comparison functions for different sorting options
                function cmpNameASC($a, $b) {
                    return strcmp($a['name'], $b['name']);
                }

                function cmpEmailDESC($a, $b) {
                    return strcmp($b['email'], $a['email']);
                }

                function cmpMajorASC($a, $b) {
                    return strcmp($a['major'], $b['major']);
                }

                function cmpGradeDESC($a, $b) {
                    if ($a['grade'] == $b['grade']) return 0;
                    return ($a['grade'] > $b['grade']) ? -1 : 1;
                }

                function cmpMajorDESCGradeASC($a, $b) {
                    // First sort by major in descending order
                    $majorCmp = strcmp($b['major'], $a['major']);
                    if ($majorCmp != 0) return $majorCmp;

                    // If majors are the same, sort by grade in ascending order
                    if ($a['grade'] == $b['grade']) return 0;
                    return ($a['grade'] > $b['grade']) ? 1 : -1;
                }

                function cmpGradeDESCMajorASCNameASC($a, $b) {
                    // First sort by grade in descending order
                    if ($a['grade'] != $b['grade']) {
                        return ($a['grade'] > $b['grade']) ? -1 : 1;
                    }

                    // If grades are the same, sort by major in ascending order
                    $majorCmp = strcmp($a['major'], $b['major']);
                    if ($majorCmp != 0) return $majorCmp;

                    // If majors are also the same, sort by name in ascending order
                    return strcmp($a['name'], $b['name']);
                }

                // Sort based on selected option
                $sortBy = $_POST['sortby'];

                switch ($sortBy) {
                    case 'byname':
                        usort($data, 'cmpNameASC');
                        echo "<h3 style='text-align:center;'>Sorted by name in ascending order:</h3>";
                        break;
                    case 'byemail':
                        usort($data, 'cmpEmailDESC');
                        echo "<h3 style='text-align:center;'>Sorted by email in descending order:</h3>";
                        break;
                    case 'bymajor':
                        usort($data, 'cmpMajorASC');
                        echo "<h3 style='text-align:center;'>Sorted by major in ascending order:</h3>";
                        break;
                    case 'bygrade':
                        usort($data, 'cmpGradeDESC');
                        echo "<h3 style='text-align:center;'>Sorted by grade in descending order:</h3>";
                        break;
                    case 'bymajorgrade':
                        usort($data, 'cmpMajorDESCGradeASC');
                        echo "<h3 style='text-align:center;'>Sorted by major in descending then by grade in ascending:</h3>";
                        break;
                    case 'bygrademajorname':
                        usort($data, 'cmpGradeDESCMajorASCNameASC');
                        echo "<h3 style='text-align:center;'>Sorted by grade in descending then by major in ascending then by name in ascending:</h3>";
                        break;
                    default:
                        usort($data, 'cmpNameASC');
                        echo "<h3 style='text-align:center;'>Sorted by name in ascending order (default):</h3>";
                        break;
                }

                displayData($data);
            }
        ?>
    </body>
</html>